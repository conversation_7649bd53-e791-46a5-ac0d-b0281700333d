import axios from 'axios';

// Define interfaces based on our API responses
interface Shift {
  id: number;
  workplaceId: number;
  workerId: number | null;
  cancelledAt: string | null;
}

interface Workplace {
  id: number;
  name: string;
}

interface WorkplaceActivity {
  name: string;
  shifts: number;
}

interface PaginatedResponse<T> {
  data: T[];
  links?: {
    next?: string;
  };
}

// Fetches all shifts from the API with pagination support
async function getAllShifts(): Promise<Shift[]> {
  const shifts: Shift[] = [];
  let nextUrl = 'http://localhost:3000/shifts';

  while (nextUrl) {
    try {
      const response = await axios.get<PaginatedResponse<Shift>>(nextUrl);
      shifts.push(...response.data.data);
      nextUrl = response.data.links?.next || '';
    } catch (error) {
      throw new Error(`Failed to fetch shifts: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  return shifts;
}

// Fetches workplace details by ID
async function getWorkplace(id: number): Promise<Workplace> {
  try {
    const response = await axios.get(`http://localhost:3000/workplaces/${id}`);
    return response.data.data;
  } catch (error) {
    throw new Error(`Failed to fetch workplace ${id}: ${error instanceof Error ? error.message : String(error)}`);
  }
}

async function getTopWorkplaces(): Promise<WorkplaceActivity[]> {
  // Get all shifts
  const shifts = await getAllShifts();

  // Filter and count completed shifts per workplace
  const completedShifts = shifts.filter(shift => shift.workerId !== null && !shift.cancelledAt);
  const workplaceShiftCounts = completedShifts.reduce<Record<number, number>>((acc, shift) => {
    acc[shift.workplaceId] = (acc[shift.workplaceId] || 0) + 1;
    return acc;
  }, {});

  // Get workplace details and create final result
  const workplacePromises = Object.entries(workplaceShiftCounts).map(
    async ([workplaceId, shiftCount]): Promise<WorkplaceActivity> => {
      const workplace = await getWorkplace(Number(workplaceId));
      return {
        name: workplace.name,
        shifts: shiftCount
      };
    }
  );

  // Wait for all workplace details to be fetched
  const results = await Promise.all(workplacePromises);

  // Sort by number of shifts (descending) and take top 3
  return results
    .sort((a, b) => b.shifts - a.shifts)
    .slice(0, 3);
}

// Main execution
async function main() {
  try {
    const topWorkplaces = await getTopWorkplaces();
    // Print in exact required format without any additional text
    process.stdout.write(JSON.stringify(topWorkplaces));
  } catch (error) {
    // Exit with error code but don't print error message
    process.exit(1);
  }
}

main();
