const http = require('http');

// Fetches all shifts from the API with pagination support
async function getAllShifts(request) {
  const shifts = [];
  let nextUrl = 'http://localhost:3000/shifts';

  while (nextUrl) {
    await new Promise((resolve) => {
      const url = new URL(nextUrl);
      const options = {
        hostname: url.hostname,
        port: url.port,
        path: url.pathname + url.search,
        method: 'GET'
      };

      const req = require('http').request(options, (res) => {
        let data = '';

        res.on('data', (chunk) => {
          data += chunk;
        });

        res.on('end', () => {
          try {
            const result = JSON.parse(data);
            shifts.push(...result.data);
            nextUrl = result.links?.next || '';
            resolve();
          } catch (err) {
            nextUrl = '';
            resolve();
          }
        });
      });

      req.on('error', (error) => {
        nextUrl = '';
        resolve();
      });

      req.end();
    });
  }

  return shifts;
}

// Fetches workplace details by ID
async function getWorkplace(id) {
  return new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: `/workplaces/${id}`,
      method: 'GET'
    };

    const req = require('http').request(options, (res) => {
      let data = '';

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          resolve(result.data);
        } catch (err) {
          resolve(null);
        }
      });
    });

    req.on('error', (error) => {
      resolve(null);
    });

    req.end();
  });
}

async function getTopWorkplaces() {
  // Get all shifts
  const shifts = await getAllShifts();

  // Filter completed shifts (has worker assigned and not cancelled) and count by workplace
  const workplaceShiftCounts = shifts
    .filter(shift => shift.workerId !== null && !shift.cancelledAt)
    .reduce((acc, shift) => {
      acc[shift.workplaceId] = (acc[shift.workplaceId] || 0) + 1;
      return acc;
    }, {});

  // Get workplace details and create final result
  const workplacePromises = Object.entries(workplaceShiftCounts).map(
    async ([workplaceId, shiftCount]) => {
      const workplace = await getWorkplace(Number(workplaceId));
      if (!workplace) {
        return null;
      }
      return {
        name: workplace.name,
        shifts: shiftCount
      };
    }
  );

  // Wait for all workplace details to be fetched
  const results = (await Promise.all(workplacePromises))
    .filter(result => result !== null);

  // Sort by number of shifts (descending) and take top 3
  return results
    .sort((a, b) => b.shifts - a.shifts)
    .slice(0, 3);
}

// Main execution
async function main() {
  try {
    const topWorkplaces = await getTopWorkplaces();
    process.stdout.write(JSON.stringify(topWorkplaces));
  } catch (error) {
    process.exit(1);
  }
}

main();
